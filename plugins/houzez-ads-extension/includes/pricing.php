<?php
/**
 * Dynamic pricing functionality for Houzez Ads Extension
 *
 * @package HouzezAdsExtension
 * @subpackage HouzezAdsExtension/includes
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}

/**
 * Calculate campaign price in credits based on zone, duration, and quantity.
 *
 * @param string $ad_zone The ad zone (homepage, sidebar, search, property_detail).
 * @param int    $duration Duration in days (7, 14, 30).
 * @param int    $quantity Number of properties (for property-specific ads).
 * @param string $ad_type The ad type (property, profile, partner).
 * @return int The calculated price in credits.
 */
function houzez_ads_calculate_campaign_price( $ad_zone, $duration, $quantity = 1, $ad_type = 'property' ) {
	$pricing = get_option( 'houzez_ads_credit_pricing', array() );

	// Default credit pricing if not set
	if ( empty( $pricing ) ) {
		$pricing = array(
			'homepage' => array(
				'7' => 49,
				'14' => 89,
				'30' => 149
			),
			'sidebar' => array(
				'7' => 29,
				'14' => 49,
				'30' => 89
			),
			'search' => array(
				'7' => 10,
				'14' => 18,
				'30' => 30
			),
			'property_detail' => array(
				'7' => 35,
				'14' => 59,
				'30' => 99
			)
		);
	}

	// Get base price for zone and duration
	$base_price = 0;
	if ( isset( $pricing[ $ad_zone ][ $duration ] ) ) {
		$base_price = $pricing[ $ad_zone ][ $duration ];
	}

	// Apply quantity multiplier for property-specific ads
	if ( $ad_type === 'property' && $ad_zone === 'search' ) {
		$base_price = $base_price * $quantity;
	}

	// Apply discounts for longer durations
	$discount = 0;
	if ( $duration >= 30 ) {
		$discount = 0.15; // 15% discount for 30+ days
	} elseif ( $duration >= 14 ) {
		$discount = 0.10; // 10% discount for 14+ days
	}

	$final_price = $base_price * ( 1 - $discount );

	/**
	 * Filter the calculated campaign price in credits.
	 *
	 * @param int    $final_price The calculated price in credits.
	 * @param string $ad_zone     The ad zone.
	 * @param int    $duration    Duration in days.
	 * @param int    $quantity    Number of properties.
	 * @param string $ad_type     The ad type.
	 */
	return absint( apply_filters( 'houzez_ads_campaign_price', $final_price, $ad_zone, $duration, $quantity, $ad_type ) );
}

/**
 * Get credit pricing table for display.
 *
 * @return array The credit pricing table.
 */
function houzez_ads_get_pricing_table() {
	return get_option( 'houzez_ads_credit_pricing', array() );
}

/**
 * Update credit pricing table.
 *
 * @param array $pricing The new credit pricing table.
 * @return bool True on success, false on failure.
 */
function houzez_ads_update_pricing_table( $pricing ) {
	return update_option( 'houzez_ads_credit_pricing', $pricing );
}

/**
 * Get formatted price for display in credits.
 *
 * @param int $credits The credits amount to format.
 * @return string The formatted credits.
 */
function houzez_ads_format_price( $credits ) {
	return houzez_ads_format_credits( $credits );
}

/**
 * Get available ad zones.
 *
 * @return array Available ad zones.
 */
function houzez_ads_get_available_zones() {
	$zones = array(
		'homepage' => __( 'Homepage Banner', 'houzez-ads-extension' ),
		'sidebar' => __( 'Sidebar (Agency/Profile)', 'houzez-ads-extension' ),
		'search' => __( 'Property Search Results', 'houzez-ads-extension' ),
		'property_detail' => __( 'Property Detail Page', 'houzez-ads-extension' )
	);

	/**
	 * Filter available ad zones.
	 *
	 * @param array $zones Available ad zones.
	 */
	return apply_filters( 'houzez_ads_available_zones', $zones );
}

/**
 * Get available ad durations.
 *
 * @return array Available durations.
 */
function houzez_ads_get_available_durations() {
	$durations = array(
		'7' => __( '1 Week (7 days)', 'houzez-ads-extension' ),
		'14' => __( '2 Weeks (14 days)', 'houzez-ads-extension' ),
		'30' => __( '1 Month (30 days)', 'houzez-ads-extension' )
	);

	/**
	 * Filter available durations.
	 *
	 * @param array $durations Available durations.
	 */
	return apply_filters( 'houzez_ads_available_durations', $durations );
}

/**
 * Get available ad types.
 *
 * @return array Available ad types.
 */
function houzez_ads_get_available_ad_types() {
	$types = array(
		'property' => __( 'Property Promotion', 'houzez-ads-extension' ),
		'profile' => __( 'Agent/Agency Profile', 'houzez-ads-extension' ),
		'partner' => __( 'Partner/Business', 'houzez-ads-extension' )
	);

	/**
	 * Filter available ad types.
	 *
	 * @param array $types Available ad types.
	 */
	return apply_filters( 'houzez_ads_available_ad_types', $types );
}

